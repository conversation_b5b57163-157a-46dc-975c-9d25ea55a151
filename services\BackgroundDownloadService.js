import * as FileSystem from 'expo-file-system';
import * as MediaLibrary from 'expo-media-library';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Serviço para downloads em background
class BackgroundDownloadService {
  static activeDownloads = new Map(); // Controle de downloads ativos
  static downloadQueue = []; // Fila de downloads
  static isProcessing = false; // Flag para evitar processamento simultâneo

  // Iniciar download em background
  static async startBackgroundDownload(downloadInfo) {
    console.log('[BACKGROUND_DOWNLOAD] Starting download with info:', {
      url: downloadInfo.url?.substring(0, 100) + '...',
      originalUrl: downloadInfo.originalUrl?.substring(0, 100) + '...',
      quality: downloadInfo.quality,
      platform: downloadInfo.platform,
      title: downloadInfo.title
    });

    const downloadId = this.generateDownloadId(downloadInfo);
    console.log('[BACKGROUND_DOWNLOAD] Generated download ID:', downloadId);

    // Verificar se já está baixando
    if (this.activeDownloads.has(downloadId)) {
      console.log('[BACKGROUND_DOWNLOAD] Download already in progress:', downloadId);
      throw new Error('Este vídeo já está sendo baixado');
    }

    // Adicionar à fila
    const downloadTask = {
      id: downloadId,
      ...downloadInfo,
      status: 'queued',
      progress: 0,
      startTime: new Date().toISOString(),
      error: null
    };

    console.log('[BACKGROUND_DOWNLOAD] Adding to queue:', downloadTask.id);
    this.downloadQueue.push(downloadTask);
    this.activeDownloads.set(downloadId, downloadTask);

    // Salvar estado
    await this.saveDownloadState();

    // Processar fila
    console.log('[BACKGROUND_DOWNLOAD] Starting queue processing');
    this.processDownloadQueue();

    return downloadId;
  }

  // Gerar ID único para download
  static generateDownloadId(downloadInfo) {
    const url = downloadInfo.url || downloadInfo.originalUrl;
    const quality = downloadInfo.quality?.quality || 'default';
    return `${url}_${quality}`.replace(/[^a-zA-Z0-9]/g, '_').substring(0, 50);
  }

  // Processar fila de downloads
  static async processDownloadQueue() {
    if (this.isProcessing) return;
    this.isProcessing = true;

    try {
      while (this.downloadQueue.length > 0) {
        const downloadTask = this.downloadQueue.shift();
        
        if (!downloadTask) continue;

        console.log(`🚀 Iniciando download em background: ${downloadTask.id}`);
        
        try {
          await this.executeDownload(downloadTask);
        } catch (error) {
          console.error(`❌ Erro no download ${downloadTask.id}:`, error);
          downloadTask.status = 'error';
          downloadTask.error = error.message;
          await this.saveDownloadState();
        }
      }
    } finally {
      this.isProcessing = false;
    }
  }

  // Executar download individual
  static async executeDownload(downloadTask) {
    try {
      console.log('[BACKGROUND_DOWNLOAD] Executing download:', downloadTask.id);
      console.log('[BACKGROUND_DOWNLOAD] Download URL:', downloadTask.url);

      downloadTask.status = 'downloading';
      await this.saveDownloadState();

      // Gerar nome do arquivo
      const fileName = this.generateFileName(downloadTask);
      const fileUri = FileSystem.documentDirectory + fileName;
      console.log('[BACKGROUND_DOWNLOAD] File will be saved to:', fileUri);

      // Callback de progresso
      const progressCallback = async (progress) => {
        const percentage = Math.round((progress.totalBytesWritten / progress.totalBytesExpectedToWrite) * 100);
        downloadTask.progress = percentage;
        downloadTask.status = 'downloading';

        console.log(`[BACKGROUND_DOWNLOAD] Progress ${downloadTask.id}: ${percentage}%`);

        // Salvar progresso a cada 10%
        if (percentage % 10 === 0) {
          await this.saveDownloadState();
        }
      };

      // Criar download resumível
      const downloadResumable = FileSystem.createDownloadResumable(
        downloadTask.url,
        fileUri,
        {
          headers: {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Referer': downloadTask.originalUrl,
            'Accept': 'video/webm,video/ogg,video/*;q=0.9,*/*;q=0.5'
          }
        },
        progressCallback
      );

      // Executar download
      console.log('[BACKGROUND_DOWNLOAD] Starting download execution...');
      const result = await downloadResumable.downloadAsync();
      console.log('[BACKGROUND_DOWNLOAD] Download result:', result);

      if (!result || !result.uri) {
        console.error('[BACKGROUND_DOWNLOAD] Download failed - no result or URI');
        throw new Error('Falha no download do arquivo');
      }

      // Verificar arquivo
      const fileInfo = await FileSystem.getInfoAsync(result.uri);
      console.log('[BACKGROUND_DOWNLOAD] File info:', fileInfo);

      if (!fileInfo.exists) {
        console.error('[BACKGROUND_DOWNLOAD] Downloaded file does not exist');
        throw new Error('Arquivo baixado não existe');
      }

      if (fileInfo.size < 10000) {
        console.error('[BACKGROUND_DOWNLOAD] Downloaded file too small:', fileInfo.size, 'bytes');
        throw new Error(`Arquivo baixado é muito pequeno (${fileInfo.size} bytes)`);
      }

      // Salvar na galeria
      console.log('[BACKGROUND_DOWNLOAD] Saving to gallery...');
      await this.saveToGallery(result.uri, fileName);
      console.log('[BACKGROUND_DOWNLOAD] Successfully saved to gallery');

      // Atualizar status
      downloadTask.status = 'completed';
      downloadTask.progress = 100;
      downloadTask.fileUri = result.uri;
      downloadTask.fileName = fileName;
      downloadTask.fileSize = fileInfo.size;
      downloadTask.completedTime = new Date().toISOString();

      console.log('[BACKGROUND_DOWNLOAD] Download task updated:', {
        id: downloadTask.id,
        status: downloadTask.status,
        fileName: downloadTask.fileName,
        fileSize: downloadTask.fileSize
      });

      // Adicionar ao histórico de downloads
      console.log('[BACKGROUND_DOWNLOAD] Adding to download history...');
      await this.addToDownloadHistory(downloadTask);

      console.log(`✅ Download concluído: ${downloadTask.id}`);

    } catch (error) {
      downloadTask.status = 'error';
      downloadTask.error = error.message;
      throw error;
    } finally {
      await this.saveDownloadState();
    }
  }

  // Gerar nome do arquivo
  static generateFileName(downloadTask) {
    const quality = downloadTask.quality?.quality || 'default';
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const extension = this.getFileExtension(downloadTask.url);
    
    return `video_${quality}_${timestamp}.${extension}`;
  }

  // Obter extensão do arquivo
  static getFileExtension(url) {
    if (url.includes('.mp4')) return 'mp4';
    if (url.includes('.webm')) return 'webm';
    if (url.includes('.avi')) return 'avi';
    if (url.includes('.mov')) return 'mov';
    return 'mp4'; // padrão
  }

  // Salvar na galeria
  static async saveToGallery(fileUri, fileName) {
    try {
      const asset = await MediaLibrary.createAssetAsync(fileUri);
      const album = await MediaLibrary.getAlbumAsync('Video Downloads');
      
      if (album == null) {
        await MediaLibrary.createAlbumAsync('Video Downloads', asset, false);
      } else {
        await MediaLibrary.addAssetsToAlbumAsync([asset], album, false);
      }
      
      console.log('✅ Vídeo salvo na galeria:', fileName);
    } catch (error) {
      console.error('❌ Erro ao salvar na galeria:', error);
      throw error;
    }
  }

  // Salvar estado dos downloads
  static async saveDownloadState() {
    try {
      const state = {
        activeDownloads: Array.from(this.activeDownloads.values()),
        queue: this.downloadQueue,
        lastUpdate: new Date().toISOString()
      };
      
      await AsyncStorage.setItem('background_downloads', JSON.stringify(state));
    } catch (error) {
      console.error('Erro ao salvar estado dos downloads:', error);
    }
  }

  // Carregar estado dos downloads
  static async loadDownloadState() {
    try {
      const stateJson = await AsyncStorage.getItem('background_downloads');
      if (stateJson) {
        const state = JSON.parse(stateJson);
        
        // Restaurar downloads ativos
        this.activeDownloads.clear();
        state.activeDownloads?.forEach(download => {
          this.activeDownloads.set(download.id, download);
        });
        
        // Restaurar fila (apenas downloads pendentes)
        this.downloadQueue = state.queue?.filter(d => d.status === 'queued') || [];
        
        console.log(`📂 Estado restaurado: ${this.activeDownloads.size} downloads ativos`);
      }
    } catch (error) {
      console.error('Erro ao carregar estado dos downloads:', error);
    }
  }

  // Adicionar ao histórico
  static async addToDownloadHistory(downloadTask) {
    try {
      const historyJson = await AsyncStorage.getItem('video_downloads');
      const history = historyJson ? JSON.parse(historyJson) : [];
      
      const historyItem = {
        id: downloadTask.id,
        fileName: downloadTask.fileName,
        uri: downloadTask.fileUri,
        size: downloadTask.fileSize,
        quality: downloadTask.quality,
        originalUrl: downloadTask.originalUrl,
        timestamp: downloadTask.completedTime,
        status: 'completed'
      };
      
      history.unshift(historyItem);
      await AsyncStorage.setItem('video_downloads', JSON.stringify(history.slice(0, 100))); // Manter apenas 100
      
    } catch (error) {
      console.error('Erro ao adicionar ao histórico:', error);
    }
  }

  // Obter status de todos os downloads
  static getDownloadStatus() {
    return {
      active: Array.from(this.activeDownloads.values()),
      queue: this.downloadQueue.length,
      processing: this.isProcessing
    };
  }

  // Cancelar download
  static async cancelDownload(downloadId) {
    const download = this.activeDownloads.get(downloadId);
    if (download) {
      download.status = 'cancelled';
      this.activeDownloads.delete(downloadId);
      
      // Remover da fila se estiver lá
      this.downloadQueue = this.downloadQueue.filter(d => d.id !== downloadId);
      
      await this.saveDownloadState();
      return true;
    }
    return false;
  }

  // Limpar downloads concluídos
  static async clearCompletedDownloads() {
    const completed = Array.from(this.activeDownloads.values()).filter(d => 
      d.status === 'completed' || d.status === 'error'
    );
    
    completed.forEach(download => {
      this.activeDownloads.delete(download.id);
    });
    
    await this.saveDownloadState();
    return completed.length;
  }

  // Inicializar serviço
  static async initialize() {
    await this.loadDownloadState();
    
    // Retomar downloads pendentes
    if (this.downloadQueue.length > 0) {
      console.log(`🔄 Retomando ${this.downloadQueue.length} downloads pendentes`);
      this.processDownloadQueue();
    }
  }
}

export default BackgroundDownloadService;
