import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  Alert,
  Dimensions,
  StatusBar,
  Text,
  Modal,
  ScrollView
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { WebView } from 'react-native-webview';
import { Ionicons } from '@expo/vector-icons';
import AsyncStorage from '@react-native-async-storage/async-storage';
import VideoThumbnailContextMenu from './VideoThumbnailContextMenu';
import AudioSessionManager from '../../services/AudioSessionManager';

const { width, height } = Dimensions.get('window');

const BrowserWebView = ({
  initialUrl = 'https://www.google.com',
  onVideoDetected,
  onDownloadRequest,
  onTitleChange,
  onUrlChange,
  showDownloadButton = true
}) => {
  const [url, setUrl] = useState(initialUrl);
  const [currentUrl, setCurrentUrl] = useState(initialUrl);
  const [canGoBack, setCanGoBack] = useState(false);
  const [canGoForward, setCanGoForward] = useState(false);
  const [loading, setLoading] = useState(false);
  const [showMenu, setShowMenu] = useState(false);
  const [history, setHistory] = useState([]);
  const [bookmarks, setBookmarks] = useState([]);
  const [showBookmarks, setShowBookmarks] = useState(false);
  const [pageTitle, setPageTitle] = useState('');
  const [isSecure, setIsSecure] = useState(false);
  const [showThumbnailMenu, setShowThumbnailMenu] = useState(false);
  const [thumbnailData, setThumbnailData] = useState(null);
  const [showQualitySelector, setShowQualitySelector] = useState(false);
  const [availableQualities, setAvailableQualities] = useState([]);
  const [currentVideoData, setCurrentVideoData] = useState(null);

  const webViewRef = useRef(null);

  useEffect(() => {
    loadBookmarks();
    initializeAudioSession();
  }, []);

  const initializeAudioSession = async () => {
    try {
      console.log('[WEBVIEW] Initializing audio session for concurrent playback');
      await AudioSessionManager.initialize();
      // Set to mix mode to allow concurrent playback with other apps
      await AudioSessionManager.setAudioMode('mix');
      console.log('[WEBVIEW] Audio session configured for concurrent playback');
    } catch (error) {
      console.error('[WEBVIEW] Failed to initialize audio session:', error);
    }
  };

  const loadBookmarks = async () => {
    try {
      const savedBookmarks = await AsyncStorage.getItem('browser_bookmarks');
      if (savedBookmarks) {
        setBookmarks(JSON.parse(savedBookmarks));
      }
    } catch (error) {
      console.error('Erro ao carregar favoritos:', error);
    }
  };

  const saveBookmarks = async (newBookmarks) => {
    try {
      await AsyncStorage.setItem('browser_bookmarks', JSON.stringify(newBookmarks));
      setBookmarks(newBookmarks);
    } catch (error) {
      console.error('Erro ao salvar favoritos:', error);
    }
  };

  const addBookmark = () => {
    const newBookmark = {
      id: Date.now().toString(),
      title: pageTitle || currentUrl,
      url: currentUrl,
      timestamp: new Date().toISOString()
    };
    
    const updatedBookmarks = [...bookmarks, newBookmark];
    saveBookmarks(updatedBookmarks);
    Alert.alert('Sucesso', 'Página adicionada aos favoritos!');
  };

  const removeBookmark = (id) => {
    const updatedBookmarks = bookmarks.filter(bookmark => bookmark.id !== id);
    saveBookmarks(updatedBookmarks);
  };

  const navigateToUrl = (targetUrl) => {
    let formattedUrl = targetUrl.trim();
    
    // Adicionar protocolo se necessário
    if (!formattedUrl.startsWith('http://') && !formattedUrl.startsWith('https://')) {
      // Se parece ser uma busca, usar Google
      if (!formattedUrl.includes('.') || formattedUrl.includes(' ')) {
        formattedUrl = `https://www.google.com/search?q=${encodeURIComponent(formattedUrl)}`;
      } else {
        formattedUrl = `https://${formattedUrl}`;
      }
    }
    
    setUrl(formattedUrl);
    setCurrentUrl(formattedUrl);
  };

  const handleNavigationStateChange = (navState) => {
    setCurrentUrl(navState.url);
    setCanGoBack(navState.canGoBack);
    setCanGoForward(navState.canGoForward);
    setLoading(navState.loading);
    setPageTitle(navState.title);
    setIsSecure(navState.url.startsWith('https://'));

    // Notificar mudanças para o componente pai (TabBrowser)
    if (onTitleChange) onTitleChange(navState.title);
    if (onUrlChange) onUrlChange(navState.url);

    // Adicionar ao histórico
    if (!loading && navState.url !== currentUrl) {
      setHistory(prev => {
        const newHistory = [...prev, {
          url: navState.url,
          title: navState.title,
          timestamp: new Date().toISOString()
        }];
        return newHistory.slice(-50); // Manter apenas os últimos 50
      });
    }
  };

  const detectVideoInPage = (html) => {
    // Detectar vídeos na página
    const videoPatterns = [
      /\.mp4/gi,
      /\.webm/gi,
      /\.avi/gi,
      /\.mov/gi,
      /youtube\.com\/watch/gi,
      /vimeo\.com/gi,
      /tiktok\.com/gi,
      /instagram\.com.*\/p\//gi,
      /facebook\.com.*\/videos/gi
    ];

    const hasVideo = videoPatterns.some(pattern => pattern.test(html));
    if (hasVideo && onVideoDetected) {
      onVideoDetected(currentUrl);
    }
  };

  const handleMessage = (event) => {
    try {
      const data = JSON.parse(event.nativeEvent.data);
      console.log('[RN] Message received from WebView:', data.type);

      if (data.type === 'pageContent') {
        detectVideoInPage(data.content);
      } else if (data.type === 'videoThumbnailLongPress') {
        console.log('[RN] Video thumbnail long press received:', data.thumbnailData);
        setThumbnailData(data.thumbnailData);
        setShowThumbnailMenu(true);
        console.log('[RN] Context menu should now be visible');
      }
    } catch (error) {
      console.error('[RN] Erro ao processar mensagem:', error);
    }
  };

  const handleDownload = () => {
    if (onDownloadRequest) {
      onDownloadRequest(url);
    } else {
      Alert.alert('Download', 'Funcionalidade de download será implementada');
    }
  };

  const handleThumbnailDownload = (thumbnailData, quality) => {
    console.log('[WEBVIEW] Thumbnail download requested:', thumbnailData, quality);
    if (onDownloadRequest) {
      onDownloadRequest(thumbnailData.videoUrl, quality, thumbnailData);
    } else {
      Alert.alert('Download', `Download solicitado para: ${thumbnailData.title}`);
    }
  };

  const handleQualitySelect = (quality) => {
    console.log('[WEBVIEW] Quality selected:', quality);
    setShowQualitySelector(false);
    if (currentVideoData && onDownloadRequest) {
      onDownloadRequest(currentVideoData.url, quality, currentVideoData);
    }
  };

  const detectVideoInPage = (content) => {
    // Simple video detection for now
    const hasVideo = content.includes('<video') || content.includes('youtube.com') || content.includes('vimeo.com');
    if (hasVideo && onVideoDetected) {
      onVideoDetected(currentUrl);
    }
  };

  const navigateToUrl = (targetUrl) => {
    let formattedUrl = targetUrl.trim();

    if (!formattedUrl.startsWith('http://') && !formattedUrl.startsWith('https://')) {
      if (formattedUrl.includes('.') && !formattedUrl.includes(' ')) {
        formattedUrl = 'https://' + formattedUrl;
      } else {
        formattedUrl = `https://www.google.com/search?q=${encodeURIComponent(formattedUrl)}`;
      }
    }

    setUrl(formattedUrl);
    setCurrentUrl(formattedUrl);
  };

  const injectedJavaScript = `
    (function() {
      console.log('[INJECT] Starting minimal thumbnail detection');

      // Test basic functionality first
      if (!window.ReactNativeWebView) {
        console.log('[INJECT] ERROR: ReactNativeWebView not available');
        return;
      }

      console.log('[INJECT] ReactNativeWebView is available');

      // Simple function to send message
      function sendMessage(element, x, y) {
        console.log('[INJECT] Sending message for element:', element.tagName);

        const data = {
          title: element.alt || element.title || 'Clicked Element',
          videoUrl: element.href || element.src || window.location.href,
          thumbnailUrl: element.src || null,
          x: x || 100,
          y: y || 100,
          platform: 'generic'
        };

        window.ReactNativeWebView.postMessage(JSON.stringify({
          type: 'videoThumbnailLongPress',
          thumbnailData: data
        }));

        console.log('[INJECT] Message sent');
      }

      // Add simple click listeners to all images
      function addClickListeners() {
        console.log('[INJECT] Adding click listeners');

        const images = document.querySelectorAll('img');
        console.log('[INJECT] Found', images.length, 'images');

        for (let i = 0; i < images.length; i++) {
          const img = images[i];

          // Add click listener (for testing)
          img.addEventListener('click', function(e) {
            console.log('[INJECT] Image clicked');
            e.preventDefault();
            sendMessage(img, e.clientX, e.clientY);
          });

          // Add right-click listener
          img.addEventListener('contextmenu', function(e) {
            console.log('[INJECT] Image right-clicked');
            e.preventDefault();
            sendMessage(img, e.clientX, e.clientY);
          });
        }

        console.log('[INJECT] Click listeners added to', images.length, 'images');
      }

      // Run immediately
      addClickListeners();

      // Run after page loads
      if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', addClickListeners);
      }

      // Run after a delay for dynamic content
      setTimeout(addClickListeners, 2000);

      // Configure web audio for concurrent playback
      function configureWebAudioForConcurrentPlayback() {
        console.log('[INJECT] Configuring web audio for concurrent playback');

        try {
          // Configure all video elements for concurrent playback
          const videos = document.querySelectorAll('video');
          videos.forEach(video => {
            // Prevent videos from pausing other audio
            video.setAttribute('playsinline', 'true');
            video.setAttribute('webkit-playsinline', 'true');

            // Add event listeners to monitor playback
            video.addEventListener('play', function() {
              console.log('[INJECT] Video started playing - concurrent mode');
            });

            video.addEventListener('pause', function() {
              console.log('[INJECT] Video paused');
            });
          });

          console.log('[INJECT] Web audio configured for concurrent playback');
        } catch (error) {
          console.log('[INJECT] Error configuring web audio:', error);
        }
      }

      // Run audio configuration
      configureWebAudioForConcurrentPlayback();

      // Re-run audio configuration when new content loads
      const observer = new MutationObserver(function(mutations) {
        let hasNewVideos = false;
        mutations.forEach(function(mutation) {
          if (mutation.addedNodes) {
            mutation.addedNodes.forEach(function(node) {
              if (node.nodeType === 1) { // Element node
                if (node.tagName === 'VIDEO' || (node.querySelector && node.querySelector('video'))) {
                  hasNewVideos = true;
                }
              }
            });
          }
        });

        if (hasNewVideos) {
          console.log('[INJECT] New videos detected, reconfiguring audio');
          setTimeout(configureWebAudioForConcurrentPlayback, 100);
          setTimeout(addClickListeners, 100);
        }
      });

      // Start observing
      observer.observe(document.body, {
        childList: true,
        subtree: true
      });

      console.log('[INJECT] Injection complete with concurrent playback support');
    })();
  `;

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="#1a1a2e" />

      {/* Barra de navegação com safe area */}
      <SafeAreaView edges={['left', 'right']} style={styles.navigationBarContainer}>
        <View style={styles.navigationBar}>
          <View style={styles.urlContainer}>
            <View style={styles.securityIndicator}>
              <Ionicons
                name={isSecure ? "lock-closed" : "lock-open"}
                size={16}
                color={isSecure ? "#4CAF50" : "#FF9800"}
              />
            </View>

            <TextInput
              style={styles.urlInput}
              value={url}
              onChangeText={setUrl}
              onSubmitEditing={() => navigateToUrl(url)}
              placeholder="Digite uma URL ou pesquise..."
              placeholderTextColor="#888"
              autoCapitalize="none"
              autoCorrect={false}
              keyboardType="url"
              returnKeyType="go"
            />
          </View>

          <TouchableOpacity
            style={styles.refreshButton}
            onPress={() => webViewRef.current?.reload()}
          >
            <Ionicons name="refresh" size={20} color="#fff" />
          </TouchableOpacity>
        </View>
      </SafeAreaView>

      {/* Indicador de carregamento */}
      {loading && (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="small" color="#6c5ce7" />
          <Text style={styles.loadingText}>Carregando...</Text>
        </View>
      )}

      {/* Botão de download flutuante */}
      {showDownloadButton && (
        <TouchableOpacity
          style={styles.downloadButton}
          onPress={handleDownload}
        >
          <Ionicons name="download" size={24} color="#fff" />
        </TouchableOpacity>
      )}

      {/* Test button for context menu */}
      <TouchableOpacity
        style={styles.testButton}
        onPress={() => {
          console.log('[TEST] Manual context menu trigger');
          setThumbnailData({
            title: 'Test Video',
            videoUrl: 'https://example.com/test.mp4',
            thumbnailUrl: null,
            x: 100,
            y: 100,
            platform: 'test'
          });
          setShowThumbnailMenu(true);
        }}
      >
        <Ionicons name="bug" size={20} color="#fff" />
      </TouchableOpacity>

      {/* Audio mode indicator */}
      <View style={styles.audioModeIndicator}>
        <Ionicons name="musical-notes" size={16} color="#6c5ce7" />
        <Text style={styles.audioModeText}>Concurrent Playback Mode</Text>
      </View>

      {/* WebView */}
      <WebView
        ref={webViewRef}
        source={{ uri: url }}
        style={styles.webview}
        onNavigationStateChange={handleNavigationStateChange}
        onMessage={handleMessage}
        injectedJavaScript={injectedJavaScript}
        onLoadStart={() => {
          setLoading(true);
          console.log('[WEBVIEW] Page load started - maintaining concurrent audio session');
        }}
        onLoadEnd={() => {
          setLoading(false);
          console.log('[WEBVIEW] Page load completed - ensuring concurrent playback');
          // Re-initialize audio session to ensure concurrent playback
          setTimeout(() => {
            AudioSessionManager.prepareForVideoPlayback();
          }, 1000);
        }}
        onError={(syntheticEvent) => {
          const { nativeEvent } = syntheticEvent;
          console.error('[WEBVIEW] WebView error: ', nativeEvent);
        }}
        onShouldStartLoadWithRequest={(request) => {
          console.log('[WEBVIEW] Loading request:', request.url);
          return true;
        }}
        allowsInlineMediaPlayback={true}
        mediaPlaybackRequiresUserAction={false}
        allowsFullscreenVideo={true}
        mixedContentMode="compatibility"
        allowsAirPlayForMediaPlayback={true}
        allowsPictureInPictureMediaPlayback={true}
        startInLoadingState={true}
        scalesPageToFit={true}
        bounces={false}
        scrollEnabled={true}
        javaScriptEnabled={true}
        domStorageEnabled={true}
        cacheEnabled={true}
        // Enhanced concurrent playback support
        allowsBackForwardNavigationGestures={true}
        decelerationRate="normal"
        // iOS specific concurrent playback settings
        allowsLinkPreview={false}
        dataDetectorTypes="none"
        // Android specific settings for better performance
        androidHardwareAccelerationDisabled={false}
        androidLayerType="hardware"
      />

      {/* Context Menu para thumbnails */}
      <VideoThumbnailContextMenu
        visible={showThumbnailMenu}
        onClose={() => setShowThumbnailMenu(false)}
        thumbnailData={thumbnailData}
        onDownloadRequest={handleThumbnailDownload}
      />

      {/* Seletor de qualidade */}
      <VideoQualitySelector
        visible={showQualitySelector}
        onClose={() => setShowQualitySelector(false)}
        onQualitySelect={handleQualitySelect}
        availableQualities={availableQualities}
        videoData={currentVideoData}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#1a1a2e',
  },
  navigationBarContainer: {
    backgroundColor: '#1a1a2e',
  },
  navigationBar: {
    flexDirection: 'row',
    paddingHorizontal: 10,
    paddingVertical: 8,
    alignItems: 'center',
  },
  urlContainer: {
    flex: 1,
    flexDirection: 'row',
    backgroundColor: '#2d2d44',
    borderRadius: 8,
    paddingHorizontal: 12,
    alignItems: 'center',
  },
  securityIndicator: {
    marginRight: 8,
  },
  urlInput: {
    flex: 1,
    color: '#fff',
    fontSize: 16,
    paddingVertical: 8,
  },
  refreshButton: {
    marginLeft: 10,
    padding: 8,
    backgroundColor: '#6c5ce7',
    borderRadius: 6,
  },
  loadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 8,
    backgroundColor: 'rgba(108, 92, 231, 0.1)',
  },
  loadingText: {
    color: '#6c5ce7',
    marginLeft: 8,
    fontSize: 14,
  },
  webview: {
    flex: 1,
  },
  downloadButton: {
    position: 'absolute',
    bottom: 20,
    right: 20,
    backgroundColor: '#6c5ce7',
    borderRadius: 30,
    padding: 15,
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    zIndex: 1000,
  },
  testButton: {
    position: 'absolute',
    bottom: 80,
    right: 20,
    backgroundColor: '#FF9800',
    borderRadius: 25,
    padding: 12,
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    zIndex: 1000,
  },
  audioModeIndicator: {
    position: 'absolute',
    top: 100,
    left: 20,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(108, 92, 231, 0.1)',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    borderWidth: 1,
    borderColor: 'rgba(108, 92, 231, 0.3)',
  },
  audioModeText: {
    color: '#6c5ce7',
    fontSize: 12,
    marginLeft: 4,
    fontWeight: '600',
  },
});

export default BrowserWebView;

          for (const el of searchElements) {
            for (const attr of dataAttrs) {
              const value = el.getAttribute(attr);
              if (value) {
                const url = value.startsWith('http') ? value :
                           value.startsWith('/') ? window.location.origin + value : value;
                if (isValidVideoUrl(url, platform)) {
                  return url;
                }
              }
            }
          }

          // Strategy 3: Iframe extraction
          const iframes = [
            element.querySelector('iframe'),
            element.parentElement?.querySelector('iframe'),
            element.closest('div')?.querySelector('iframe')
          ].filter(Boolean);

          for (const iframe of iframes) {
            const src = iframe.src || iframe.getAttribute('data-src');
            if (src && isValidVideoUrl(src, platform)) {
              return src;
            }
          }

          return null;
        }

        // Check if URL is a valid video URL
        function isValidVideoUrl(url, platform) {
          if (!url || typeof url !== 'string') return false;

          // Platform-specific patterns
          const platformConfig = PLATFORM_PATTERNS[platform];
          if (platformConfig && platformConfig.urlPatterns) {
            return platformConfig.urlPatterns.some(pattern => pattern.test(url));
          }

          // Generic video URL patterns
          const genericPatterns = [
            /\\.(mp4|webm|ogg|avi|mov|wmv|flv|mkv|m4v)(\\?[^"'\\s]*)?$/i,
            /\\/video\\/[^\\/\\s"']+/i,
            /\\/watch\\?[^"'\\s]*v=/i,
            /\\/embed\\/[^\\/\\s"']+/i,
            /\\/player\\/[^\\/\\s"']+/i,
            /\\/stream\\/[^\\/\\s"']+/i
          ];

          return genericPatterns.some(pattern => pattern.test(url)) ||
                 url.includes('video') || url.includes('watch') || url.includes('embed');
        }

        // Enhanced video information extraction
        function extractVideoInfo(element) {
          const info = {
            title: null,
            videoUrl: null,
            thumbnailUrl: null,
            duration: null,
            platform: null,
            x: 0,
            y: 0
          };

          // Get position
          const rect = element.getBoundingClientRect();
          info.x = rect.left + rect.width / 2;
          info.y = rect.top + rect.height / 2;

          // Get platform patterns
          const platformPatterns = getPlatformPatterns();
          info.platform = platformPatterns.platform;

          // Extract video URL
          info.videoUrl = extractVideoUrl(element, info.platform);

          // Extract title with platform-specific and generic methods
          info.title = extractTitle(element, platformPatterns);

          // Extract thumbnail URL
          info.thumbnailUrl = extractThumbnailUrl(element);

          // Extract duration
          info.duration = extractDuration(element, platformPatterns);

          return info;
        }

        // Enhanced title extraction
        function extractTitle(element, platformPatterns) {
          const searchContainers = [
            element,
            element.parentElement,
            element.parentElement?.parentElement,
            element.closest('article, .video-item, .media-object, .video-card, .video-container, div')
          ].filter(Boolean);

          // Platform-specific title extraction
          if (platformPatterns.titleSelectors) {
            for (const container of searchContainers) {
              for (const selector of platformPatterns.titleSelectors) {
                try {
                  const titleEl = container.querySelector(selector);
                  if (titleEl) {
                    const text = titleEl.textContent?.trim();
                    if (text && text.length > 3 && text.length < 200) {
                      return text;
                    }
                  }
                } catch (e) {
                  // Continue to next selector
                }
              }
            }
          }

          // Generic title extraction
          const genericSelectors = [
            'h1', 'h2', 'h3', '.title', '.video-title',
            '.media-title', '.content-title', '.headline',
            '[title]', '[aria-label]', '[alt]'
          ];

          for (const container of searchContainers) {
            for (const selector of genericSelectors) {
              try {
                const titleEl = container.querySelector(selector);
                if (titleEl) {
                  const text = titleEl.textContent?.trim() ||
                              titleEl.getAttribute('title') ||
                              titleEl.getAttribute('aria-label') ||
                              titleEl.getAttribute('alt');
                  if (text && text.length > 3 && text.length < 200) {
                    return text;
                  }
                }
              } catch (e) {
                // Continue to next selector
              }
            }
          }

          // Fallback to link text
          const link = element.closest('a');
          if (link) {
            const text = link.textContent?.trim();
            if (text && text.length > 3 && text.length < 200) {
              return text;
            }
          }

          return 'Video';
        }

        // Extract thumbnail URL
        function extractThumbnailUrl(element) {
          if (element.tagName === 'IMG') {
            return element.src || element.getAttribute('data-src') || element.getAttribute('data-original');
          }

          const img = element.querySelector('img');
          if (img) {
            return img.src || img.getAttribute('data-src') || img.getAttribute('data-original');
          }

          // Check for background images
          try {
            const style = window.getComputedStyle(element);
            const bgImage = style.backgroundImage;
            if (bgImage && bgImage !== 'none') {
              const match = bgImage.match(/url\\(["']?([^"')]+)["']?\\)/);
              if (match) return match[1];
            }
          } catch (e) {
            // Ignore style errors
          }

          return null;
        }

        // Extract duration
        function extractDuration(element, platformPatterns) {
          const searchContainers = [
            element,
            element.parentElement,
            element.parentElement?.parentElement,
            element.closest('div, article')
          ].filter(Boolean);

          // Platform-specific duration extraction
          if (platformPatterns.durationSelectors) {
            for (const container of searchContainers) {
              for (const selector of platformPatterns.durationSelectors) {
                try {
                  const durationEl = container.querySelector(selector);
                  if (durationEl) {
                    const text = durationEl.textContent?.trim() ||
                                durationEl.getAttribute('data-duration');
                    if (text && text.match(/\\d+:\\d+/)) {
                      return text;
                    }
                  }
                } catch (e) {
                  // Continue to next selector
                }
              }
            }
          }

          // Generic duration extraction
          const genericSelectors = [
            '.duration', '.time', '.length', '.runtime',
            '.video-duration', '.media-duration', '[data-duration]'
          ];

          for (const container of searchContainers) {
            for (const selector of genericSelectors) {
              try {
                const durationEl = container.querySelector(selector);
                if (durationEl) {
                  const text = durationEl.textContent?.trim() ||
                              durationEl.getAttribute('data-duration');
                  if (text && text.match(/\\d+:\\d+/)) {
                    return text;
                  }
                }
              } catch (e) {
                // Continue to next selector
              }
            }
          }

          return null;
        }

        // Enhanced universal video thumbnail detection
        function isVideoThumbnail(element) {
          if (!element || !element.tagName) return false;

          const elementClasses = element.className?.toLowerCase() || '';
          const elementId = element.id?.toLowerCase() || '';
          const elementTag = element.tagName.toLowerCase();

          // Get element hierarchy for context
          const hierarchy = getElementHierarchy(element, 3);
          const hierarchyText = hierarchy.map(el =>
            el.tagName.toLowerCase() + ' ' + (el.className || '') + ' ' + (el.id || '')
          ).join(' ').toLowerCase();

          // Get platform-specific patterns
          const platformPatterns = getPlatformPatterns();

          // Platform-specific detection
          if (platformPatterns.selectors) {
            for (const selector of platformPatterns.selectors) {
              try {
                if (element.matches(selector) || element.closest(selector)) {
                  return true;
                }
              } catch (e) {
                // Ignore invalid selectors
              }
            }
          }

          // Universal indicator detection
          const hasVideoIndicator = UNIVERSAL_VIDEO_INDICATORS.some(indicator =>
            elementClasses.includes(indicator) ||
            elementId.includes(indicator) ||
            hierarchyText.includes(indicator)
          );

          // Image-specific checks
          if (elementTag === 'img') {
            const src = element.src?.toLowerCase() || '';
            const alt = element.alt?.toLowerCase() || '';

            // Check for video-related image sources
            const hasVideoSrc = [
              'thumbnail', 'preview', 'poster', 'cover',
              'maxresdefault', 'hqdefault', 'mqdefault',
              'vi_webp', 'ytimg.com', 'vimeocdn.com',
              'twimg.com', 'fbcdn.net', 'cdninstagram.com'
            ].some(pattern => src.includes(pattern));

            const hasVideoAlt = [
              'video', 'thumbnail', 'preview', 'play'
            ].some(pattern => alt.includes(pattern));

            if (hasVideoSrc || hasVideoAlt) return true;
          }

          // Video element detection
          if (elementTag === 'video') {
            return true;
          }

          // Container detection
          const isVideoContainer = element.querySelector('video, iframe[src*="youtube"], iframe[src*="vimeo"], iframe[src*="embed"]');
          if (isVideoContainer) return true;

          // Link detection
          const linkElement = element.closest('a') || element.querySelector('a');
          if (linkElement && linkElement.href) {
            const href = linkElement.href.toLowerCase();

            // Platform-specific URL patterns
            if (platformPatterns.urlPatterns) {
              const matchesPattern = platformPatterns.urlPatterns.some(pattern =>
                pattern.test(href)
              );
              if (matchesPattern) return true;
            }

            // Generic video URL patterns
            const hasVideoUrl = [
              'watch', 'video', 'play', 'stream', 'embed',
              'youtube.com', 'vimeo.com', 'dailymotion.com',
              'tiktok.com', 'instagram.com', 'facebook.com',
              'twitter.com', 'twitch.tv'
            ].some(pattern => href.includes(pattern));

            if (hasVideoUrl) return true;
          }

          // Data attribute detection
          const hasVideoData = [
            'data-video-id', 'data-video-url', 'data-embed-url',
            'data-youtube-id', 'data-vimeo-id', 'data-player-id',
            'data-media-id', 'data-shortcode'
          ].some(attr => element.hasAttribute(attr));

          if (hasVideoData) return true;

          // Enhanced play button detection
          const hasPlayButton = element.querySelector('[class*="play"], [class*="overlay"], [class*="duration"]') ||
                               element.parentElement?.querySelector('[class*="play"], [class*="overlay"], [class*="duration"]') ||
                               element.closest('div')?.querySelector('[class*="play"], [class*="overlay"], [class*="duration"]');

          if (hasPlayButton && (elementTag === 'img' || hasVideoIndicator)) return true;

          return hasVideoIndicator;
        }

        // Get element hierarchy for context analysis
        function getElementHierarchy(element, depth = 3) {
          const hierarchy = [];
          let current = element;

          for (let i = 0; i < depth && current; i++) {
            hierarchy.push({
              tagName: current.tagName,
              className: current.className,
              id: current.id
            });
            current = current.parentElement;
          }

          return hierarchy;
        }

        // Improved long press detection with better reliability
        function clearLongPressTimer() {
          if (longPressTimer) {
            clearTimeout(longPressTimer);
            longPressTimer = null;
          }
        }

        function calculateDistance(x1, y1, x2, y2) {
          return Math.sqrt(Math.pow(x2 - x1, 2) + Math.pow(y2 - y1, 2));
        }

        // Simplified trigger function that always works
        function triggerVideoThumbnailMenu(element, clientX, clientY) {
          console.log('[MENU] Triggering context menu for element:', element.tagName, element.className);

          // Create basic thumbnail data - always show menu for testing
          const thumbnailData = {
            title: element.alt || element.title || 'Video Thumbnail',
            videoUrl: element.href || element.src || window.location.href,
            thumbnailUrl: element.src || null,
            duration: null,
            platform: 'generic',
            x: clientX || 100,
            y: clientY || 100
          };

          console.log('[MENU] Thumbnail data:', thumbnailData);

          try {
            console.log('[MENU] Sending message to React Native...');
            if (window.ReactNativeWebView && window.ReactNativeWebView.postMessage) {
              window.ReactNativeWebView.postMessage(JSON.stringify({
                type: 'videoThumbnailLongPress',
                thumbnailData: thumbnailData
              }));
              console.log('[MENU] Message sent successfully');
            } else {
              console.log('[MENU] ReactNativeWebView not available');
            }
          } catch (err) {
            console.log('[MENU] Error sending message:', err);
          }
        }

        // Simplified thumbnail detection for testing
        function addVideoThumbnailListeners() {
          console.log('[THUMBNAIL] Adding simplified video thumbnail listeners...');

          // Start with basic selectors that are most likely to work
          const basicSelectors = [
            'img',
            'video',
            'a[href*="youtube"]',
            'a[href*="watch"]',
            'a[href*="video"]',
            '[class*="video"]',
            '[class*="thumbnail"]'
          ];

          console.log('[THUMBNAIL] Using basic selectors:', basicSelectors);

          let totalElements = 0;

          // Simplified element collection
          for (const selector of basicSelectors) {
            try {
              const elements = document.querySelectorAll(selector);
              console.log('[THUMBNAIL] Found ' + elements.length + ' elements for selector: ' + selector);
              totalElements += elements.length;

              // Add listeners to ALL elements found (simplified approach)
              for (let i = 0; i < elements.length; i++) {
                const element = elements[i];
                if (!processedElements.has(element)) {
                  console.log('[THUMBNAIL] Adding listeners to element:', element.tagName, element.className);
                  processedElements.add(element);
                  addThumbnailEventListeners(element);
                }
              }
            } catch (e) {
              console.log('[THUMBNAIL] Error with selector:', selector, e);
            }
          }

          console.log('[THUMBNAIL] Total elements processed: ' + totalElements);

        }

        // Simplified event listener function
        function addThumbnailEventListeners(element) {
          console.log('[LISTENERS] Adding event listeners to:', element.tagName, element.className);

          // Simplified touch start handler
          const touchStartHandler = (e) => {
            console.log('[TOUCH] Touch start on element:', element.tagName);
            e.stopPropagation();

            const touch = e.touches[0];
            touchStartTime = Date.now();
            touchStartPosition = { x: touch.clientX, y: touch.clientY };
            hasMoved = false;

            console.log('[TOUCH] Setting long press timer');
            longPressTimer = setTimeout(() => {
              if (!hasMoved) {
                console.log('[TOUCH] Long press detected! Triggering context menu');
                e.preventDefault();
                triggerVideoThumbnailMenu(element, touchStartPosition.x, touchStartPosition.y);
              }
            }, LONG_PRESS_DURATION);
          };

          // Simplified touch move handler
          const touchMoveHandler = (e) => {
            if (touchStartPosition && e.touches[0]) {
              const touch = e.touches[0];
              const distance = Math.sqrt(
                Math.pow(touch.clientX - touchStartPosition.x, 2) +
                Math.pow(touch.clientY - touchStartPosition.y, 2)
              );

              if (distance > MOVE_THRESHOLD) {
                hasMoved = true;
                if (longPressTimer) {
                  clearTimeout(longPressTimer);
                  longPressTimer = null;
                }
              }
            }
          };

          // Simplified touch end handler
          const touchEndHandler = (e) => {
            if (longPressTimer) {
              clearTimeout(longPressTimer);
              longPressTimer = null;
            }
            console.log('[TOUCH] Touch end');
          };

          // Add event listeners
          element.addEventListener('touchstart', touchStartHandler, { passive: false });
          element.addEventListener('touchmove', touchMoveHandler, { passive: true });
          element.addEventListener('touchend', touchEndHandler, { passive: true });

          // Right-click for desktop
          element.addEventListener('contextmenu', (e) => {
            console.log('[MOUSE] Right-click detected');
            e.preventDefault();
            e.stopPropagation();
            triggerVideoThumbnailMenu(element, e.clientX, e.clientY);
          });

          console.log('[LISTENERS] Event listeners added successfully');
        }

              // Enhanced visual feedback with cross-platform support
              const addVisualFeedback = () => {
                // Use transform instead of opacity for better performance
                element.style.transition = 'transform 0.1s ease, opacity 0.1s ease';

                setTimeout(() => {
                  if (longPressTimer) {
                    element.style.transform = 'scale(0.95)';
                    element.style.opacity = '0.8';

                    // Add subtle border for better feedback
                    const originalBorder = element.style.border;
                    element.style.border = '2px solid rgba(108, 92, 231, 0.5)';
                    element.setAttribute('data-original-border', originalBorder);
                  }
                }, 100);
              };

              const removeVisualFeedback = () => {
                element.style.transform = '';
                element.style.opacity = '';
                element.style.transition = '';

                // Restore original border
                const originalBorder = element.getAttribute('data-original-border');
                if (originalBorder !== null) {
                  element.style.border = originalBorder;
                  element.removeAttribute('data-original-border');
                } else {
                  element.style.border = '';
                }
              };

              // Add feedback on touch start
              element.addEventListener('touchstart', addVisualFeedback, { passive: true });

              // Remove feedback on touch end/cancel
              element.addEventListener('touchend', removeVisualFeedback, { passive: true });
              element.addEventListener('touchcancel', removeVisualFeedback, { passive: true });
            }
          });
        }

        // Initial setup
        addVideoThumbnailListeners();

        // Performance optimization setup
        const PERFORMANCE_CONFIG = {
          maxElementsPerBatch: navigator.hardwareConcurrency <= 2 ? 25 : 50,
          batchProcessingDelay: navigator.hardwareConcurrency <= 2 ? 200 : 100,
          debounceDelay: navigator.hardwareConcurrency <= 2 ? 500 : 300,
          throttleDelay: 200,
          maxMutationBatchSize: navigator.hardwareConcurrency <= 2 ? 50 : 100
        };

        // Optimized debounce function
        function createDebounce(func, delay) {
          let timeoutId;
          let lastCallTime = 0;

          return function(...args) {
            const now = Date.now();
            clearTimeout(timeoutId);

            if (now - lastCallTime > delay * 2) {
              lastCallTime = now;
              return func.apply(this, args);
            }

            timeoutId = setTimeout(() => {
              lastCallTime = Date.now();
              func.apply(this, args);
            }, delay);
          };
        }

        // Optimized throttle function
        function createThrottle(func, delay) {
          let lastCallTime = 0;
          let timeoutId;

          return function(...args) {
            const now = Date.now();

            if (now - lastCallTime >= delay) {
              lastCallTime = now;
              return func.apply(this, args);
            }

            if (!timeoutId) {
              timeoutId = setTimeout(() => {
                lastCallTime = Date.now();
                func.apply(this, args);
                timeoutId = null;
              }, delay - (now - lastCallTime));
            }
          };
        }

        // Batch processing for elements
        function processElementsBatch(elements, processor) {
          const batchSize = PERFORMANCE_CONFIG.maxElementsPerBatch;
          let currentIndex = 0;

          function processBatch() {
            const endIndex = Math.min(currentIndex + batchSize, elements.length);

            for (let i = currentIndex; i < endIndex; i++) {
              try {
                processor(elements[i]);
              } catch (error) {
                console.warn('Error processing element:', error);
              }
            }

            currentIndex = endIndex;

            if (currentIndex < elements.length) {
              setTimeout(processBatch, PERFORMANCE_CONFIG.batchProcessingDelay);
            }
          }

          processBatch();
        }

        // Debounced listener setup
        const debouncedAddListeners = createDebounce(() => {
          addVideoThumbnailListeners();
        }, PERFORMANCE_CONFIG.debounceDelay);

        // Enhanced mutation observer for dynamic content
        const observer = new MutationObserver((mutations) => {
          let shouldUpdate = false;
          let relevantMutations = 0;

          mutations.forEach(mutation => {
            if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
              mutation.addedNodes.forEach(node => {
                if (node.nodeType === Node.ELEMENT_NODE) {
                  relevantMutations++;

                  // Check for video-related content
                  const hasRelevantContent =
                    node.tagName === 'IMG' ||
                    node.tagName === 'VIDEO' ||
                    node.querySelector?.('img, video, iframe') ||
                    UNIVERSAL_VIDEO_INDICATORS.some(indicator =>
                      node.className?.toLowerCase().includes(indicator) ||
                      node.querySelector?.('[class*="' + indicator + '"]')
                    ) ||
                    // Platform-specific checks
                    node.querySelector?.('ytd-thumbnail, .video-card, .media-object, [data-video-id]');

                  if (hasRelevantContent) {
                    shouldUpdate = true;
                  }
                }
              });
            }

            // Also check for attribute changes that might affect video detection
            if (mutation.type === 'attributes' &&
                ['class', 'data-video-id', 'data-video-url', 'src'].includes(mutation.attributeName)) {
              shouldUpdate = true;
            }
          });

          // Only update if we have relevant mutations and not too many at once
          if (shouldUpdate && relevantMutations < 100) {
            debouncedAddListeners();
          }
        });

        observer.observe(document.body, {
          childList: true,
          subtree: true,
          attributes: true,
          attributeFilter: ['class', 'data-video-id', 'data-video-url', 'src']
        });

        // Enhanced scroll-based detection with optimized throttling
        const throttledScrollHandler = createThrottle(() => {
          debouncedAddListeners();
        }, PERFORMANCE_CONFIG.throttleDelay);

        window.addEventListener('scroll', throttledScrollHandler, { passive: true });

        // Optimized resize handler
        const throttledResizeHandler = createThrottle(() => {
          debouncedAddListeners();
        }, 1000);

        window.addEventListener('resize', throttledResizeHandler, { passive: true });

        // Detect on focus (when user returns to tab)
        document.addEventListener('visibilitychange', () => {
          if (!document.hidden) {
            setTimeout(() => {
              addVideoThumbnailListeners();
            }, 500);
          }
        });

        // Test function to verify injection works
        function testInjection() {
          console.log('[TEST] Testing injection - adding click listener to body');
          document.body.addEventListener('click', function(e) {
            console.log('[TEST] Body clicked at:', e.clientX, e.clientY);
            console.log('[TEST] Target element:', e.target.tagName, e.target.className);
          });
        }

        // Run test immediately
        testInjection();

        // Initial setup
        console.log('[INIT] Running initial thumbnail listener setup');
        addVideoThumbnailListeners();

        // Delayed setup for dynamic content
        setTimeout(() => {
          console.log('[INIT] Running delayed thumbnail listener setup (2s)');
          addVideoThumbnailListeners();
        }, 2000);

        setTimeout(() => {
          console.log('[INIT] Running delayed thumbnail listener setup (5s)');
          addVideoThumbnailListeners();
        }, 5000);

        // Enviar conteúdo da página para detecção de vídeo
        setTimeout(() => {
          try {
            window.ReactNativeWebView.postMessage(JSON.stringify({
              type: 'pageContent',
              content: document.documentElement.outerHTML
            }));
          } catch (e) {
            console.log('Error sending page content:', e);
          }
        }, 2000);

        // Ad Block básico
        const adSelectors = [
          '[id*="ad"]',
          '[class*="ad"]',
          '[id*="banner"]',
          '[class*="banner"]',
          'iframe[src*="doubleclick"]',
          'iframe[src*="googlesyndication"]',
          '.advertisement',
          '.ads',
          '.ad-container'
        ];

        const blockAds = () => {
          adSelectors.forEach(selector => {
            try {
              const elements = document.querySelectorAll(selector);
              elements.forEach(el => {
                if (el) el.style.display = 'none';
              });
            } catch (e) {
              console.log('Error blocking ads:', e);
            }
          });
        };

        // Executar bloqueio de anúncios imediatamente e após carregamento
        blockAds();
        if (document.readyState === 'loading') {
          document.addEventListener('DOMContentLoaded', blockAds);
        }

        // Melhorar performance de vídeos
        const videos = document.querySelectorAll('video');
        videos.forEach(video => {
          video.preload = 'metadata';
        });

      } catch (error) {
        console.log('Error in injected script:', error);
      }
    })();
    true;
  `;

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="#1a1a2e" />
      
      {/* Barra de navegação com safe area */}
      <SafeAreaView edges={['left', 'right']} style={styles.navigationBarContainer}>
        <View style={styles.navigationBar}>
          <View style={styles.urlContainer}>
            <View style={styles.securityIndicator}>
              <Ionicons
                name={isSecure ? "lock-closed" : "lock-open"}
                size={16}
                color={isSecure ? "#4CAF50" : "#FF9800"}
              />
            </View>

            <TextInput
              style={styles.urlInput}
              value={url}
              onChangeText={setUrl}
              onSubmitEditing={() => navigateToUrl(url)}
              placeholder="Digite uma URL ou pesquise..."
              placeholderTextColor="#888"
              autoCapitalize="none"
              autoCorrect={false}
              selectTextOnFocus
            />

            <TouchableOpacity
              style={styles.refreshButton}
              onPress={() => webViewRef.current?.reload()}
            >
              <Ionicons name="refresh" size={20} color="#fff" />
            </TouchableOpacity>
          </View>

          <View style={styles.navigationButtons}>
            <TouchableOpacity
              style={[styles.navButton, !canGoBack && styles.navButtonDisabled]}
              onPress={() => webViewRef.current?.goBack()}
              disabled={!canGoBack}
            >
              <Ionicons name="arrow-back" size={20} color={canGoBack ? "#fff" : "#666"} />
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.navButton, !canGoForward && styles.navButtonDisabled]}
              onPress={() => webViewRef.current?.goForward()}
              disabled={!canGoForward}
            >
              <Ionicons name="arrow-forward" size={20} color={canGoForward ? "#fff" : "#666"} />
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.navButton}
              onPress={() => setShowMenu(true)}
            >
              <Ionicons name="menu" size={20} color="#fff" />
            </TouchableOpacity>
          </View>
        </View>
      </SafeAreaView>

      {/* Botão de download flutuante */}
      {showDownloadButton && (
        <TouchableOpacity 
          style={styles.downloadButton}
          onPress={() => onDownloadRequest && onDownloadRequest(currentUrl)}
        >
          <Ionicons name="download" size={24} color="#fff" />
        </TouchableOpacity>
      )}



      {/* Menu do navegador */}
      <Modal
        visible={showMenu}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setShowMenu(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.menuContainer}>
            <View style={styles.menuHeader}>
              <Text style={styles.menuTitle}>Menu do Navegador</Text>
              <TouchableOpacity onPress={() => setShowMenu(false)}>
                <Ionicons name="close" size={24} color="#fff" />
              </TouchableOpacity>
            </View>
            
            <ScrollView style={styles.menuContent}>
              <TouchableOpacity style={styles.menuItem} onPress={addBookmark}>
                <Ionicons name="bookmark" size={20} color="#fff" />
                <Text style={styles.menuItemText}>Adicionar aos Favoritos</Text>
              </TouchableOpacity>
              
              <TouchableOpacity 
                style={styles.menuItem} 
                onPress={() => {
                  setShowMenu(false);
                  setShowBookmarks(true);
                }}
              >
                <Ionicons name="bookmarks" size={20} color="#fff" />
                <Text style={styles.menuItemText}>Ver Favoritos</Text>
              </TouchableOpacity>
              
              <TouchableOpacity style={styles.menuItem}>
                <Ionicons name="time" size={20} color="#fff" />
                <Text style={styles.menuItemText}>Histórico</Text>
              </TouchableOpacity>
              
              <TouchableOpacity style={styles.menuItem}>
                <Ionicons name="settings" size={20} color="#fff" />
                <Text style={styles.menuItemText}>Configurações</Text>
              </TouchableOpacity>
            </ScrollView>
          </View>
        </View>
      </Modal>

      {/* Modal de favoritos */}
      <Modal
        visible={showBookmarks}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setShowBookmarks(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.bookmarksContainer}>
            <View style={styles.menuHeader}>
              <Text style={styles.menuTitle}>Favoritos</Text>
              <TouchableOpacity onPress={() => setShowBookmarks(false)}>
                <Ionicons name="close" size={24} color="#fff" />
              </TouchableOpacity>
            </View>
            
            <ScrollView style={styles.bookmarksList}>
              {bookmarks.map((bookmark) => (
                <View key={bookmark.id} style={styles.bookmarkItem}>
                  <TouchableOpacity 
                    style={styles.bookmarkContent}
                    onPress={() => {
                      navigateToUrl(bookmark.url);
                      setShowBookmarks(false);
                    }}
                  >
                    <Text style={styles.bookmarkTitle}>{bookmark.title}</Text>
                    <Text style={styles.bookmarkUrl}>{bookmark.url}</Text>
                  </TouchableOpacity>
                  
                  <TouchableOpacity 
                    style={styles.deleteBookmark}
                    onPress={() => removeBookmark(bookmark.id)}
                  >
                    <Ionicons name="trash" size={16} color="#ff4444" />
                  </TouchableOpacity>
                </View>
              ))}
              
              {bookmarks.length === 0 && (
                <Text style={styles.emptyMessage}>Nenhum favorito salvo</Text>
              )}
            </ScrollView>
          </View>
        </View>
      </Modal>

      {/* Video Thumbnail Context Menu */}
      <VideoThumbnailContextMenu
        visible={showThumbnailMenu}
        onClose={() => setShowThumbnailMenu(false)}
        thumbnailData={thumbnailData}
        onDownloadRequest={onDownloadRequest}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#1a1a2e',
  },
  navigationBarContainer: {
    backgroundColor: '#1a1a2e',
  },
  navigationBar: {
    flexDirection: 'row',
    paddingHorizontal: 10,
    paddingVertical: 8,
    alignItems: 'center',
  },
  urlContainer: {
    flex: 1,
    flexDirection: 'row',
    backgroundColor: '#2d2d44',
    borderRadius: 25,
    alignItems: 'center',
    paddingHorizontal: 12,
    marginRight: 10,
  },
  securityIndicator: {
    marginRight: 8,
  },
  urlInput: {
    flex: 1,
    color: '#fff',
    fontSize: 14,
    paddingVertical: 10,
  },
  refreshButton: {
    padding: 5,
  },
  navigationButtons: {
    flexDirection: 'row',
  },
  navButton: {
    padding: 8,
    marginLeft: 5,
  },
  navButtonDisabled: {
    opacity: 0.5,
  },
  downloadButton: {
    position: 'absolute',
    right: 20,
    bottom: 100,
    backgroundColor: '#6c5ce7',
    width: 56,
    height: 56,
    borderRadius: 28,
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    zIndex: 1000,
  },
  webview: {
    flex: 1,
    backgroundColor: '#fff',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.5)',
    justifyContent: 'flex-end',
  },
  menuContainer: {
    backgroundColor: '#1a1a2e',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    maxHeight: height * 0.6,
  },
  bookmarksContainer: {
    backgroundColor: '#1a1a2e',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    maxHeight: height * 0.8,
  },
  menuHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#2d2d44',
  },
  menuTitle: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
  },
  menuContent: {
    padding: 20,
  },
  menuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#2d2d44',
  },
  menuItemText: {
    color: '#fff',
    fontSize: 16,
    marginLeft: 15,
  },
  bookmarksList: {
    padding: 20,
  },
  bookmarkItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 10,
    borderBottomWidth: 1,
    borderBottomColor: '#2d2d44',
  },
  bookmarkContent: {
    flex: 1,
  },
  bookmarkTitle: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  bookmarkUrl: {
    color: '#888',
    fontSize: 12,
    marginTop: 2,
  },
  deleteBookmark: {
    padding: 10,
  },
  emptyMessage: {
    color: '#888',
    textAlign: 'center',
    marginTop: 50,
    fontSize: 16,
  },
});

export default BrowserWebView;
